import React, { useState } from 'react';
import { Node } from 'reactflow';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useQuery } from '@tanstack/react-query';
import { Credential, InputSchema } from '@/types/workflow';
import { Key } from 'lucide-react';
import CodeEditor from '@/components/ui/code-editor';
import SchemaBuilder from '../SchemaBuilder';
import FormRenderer from '../FormRenderer';

interface NodeConfigModalProps {
  node: Node;
  onClose: () => void;
  onUpdate: (nodeId: string, data: any) => void;
}

const NodeConfigModal: React.FC<NodeConfigModalProps> = ({ node, onClose, onUpdate }) => {
  const { data: credentials } = useQuery<Credential[]>({
    queryKey: ['/api/credentials'],
  });

  const [formData, setFormData] = useState(() => {
    // Initialize form data based on node type and existing data
    switch(node.type) {
      case 'input':
        // Handle both new and legacy schema formats
        let inputSchema: InputSchema;
        if (node.data.schema && typeof node.data.schema === 'object' && 'fields' in node.data.schema) {
          // New schema format
          inputSchema = node.data.schema as InputSchema;
        } else {
          // Legacy schema format - convert to new format
          const legacySchema = node.data.schema || { "query": "string" };
          inputSchema = {
            version: "1.0",
            title: "Input Form",
            description: "Workflow input parameters",
            fields: Object.entries(legacySchema).map(([name, type], index) => ({
              id: `field_${index}`,
              name,
              label: name.charAt(0).toUpperCase() + name.slice(1),
              type: type as any,
              validation: { required: type === 'string' || type === 'number' }
            }))
          };
        }

        return {
          name: node.data.name || `Input Node`,
          description: node.data.description || 'Accepts input data for the workflow',
          schema: inputSchema,
          legacySchema: JSON.stringify(node.data.legacySchema || node.data.schema || { "query": "string" }, null, 2)
        };
      case 'prompt':
        return {
          name: node.data.name || `Prompt Node`,
          description: node.data.description || 'Processes text through an LLM prompt',
          prompt: node.data.prompt || '',
          provider: node.data.provider || 'google',
          model: node.data.model || 'Gemini 2.0 Flash',
          credentialId: node.data.credentialId || '',
          temperature: node.data.temperature || 0.7,
          maxTokens: node.data.maxTokens || 1000,
          outputFormat: node.data.outputFormat || 'text'
        };
      case 'agent':
        return {
          name: node.data.name || `Agent Node`,
          description: node.data.description || 'Uses an LLM with a system role',
          systemPrompt: node.data.systemPrompt || '',
          provider: node.data.provider || 'google',
          model: node.data.model || 'Gemini 2.0 Flash',
          credentialId: node.data.credentialId || '',
          temperature: node.data.temperature || 0.7,
          maxTokens: node.data.maxTokens || 1000,
          outputFormat: node.data.outputFormat || 'text',
          schema: JSON.stringify(node.data.schema || {}, null, 2)
        };
      case 'api':
        return {
          name: node.data.name || `API Node`,
          description: node.data.description || 'Makes an HTTP request to an API endpoint',
          url: node.data.url || 'https://api.example.com/endpoint',
          method: node.data.method || 'POST',
          headers: JSON.stringify(node.data.headers || { "Content-Type": "application/json" }, null, 2),
          authType: node.data.authType || 'none',
          apiKeyHeader: node.data.apiKeyHeader || 'x-api-key',
          credentialId: node.data.credentialId || '',
          rateLimit: {
            enabled: node.data.rateLimit?.enabled || false,
            requestsPerMinute: node.data.rateLimit?.requestsPerMinute || 60
          },
          timeout: {
            enabled: node.data.timeout?.enabled || false,
            milliseconds: node.data.timeout?.milliseconds || 30000
          },
          requestValidation: {
            enabled: node.data.requestValidation?.enabled || false,
            schema: JSON.stringify(node.data.requestValidation?.schema || {}, null, 2)
          },
          responseFormat: {
            type: node.data.responseFormat?.type || 'json',
            schema: JSON.stringify(node.data.responseFormat?.schema || {}, null, 2)
          }
        };
      case 'api-trigger':
        return {
          name: node.data.name || `API Trigger`,
          description: node.data.description || 'Receives webhook requests to trigger workflow',
          allowedMethods: node.data.allowedMethods || ['POST'],
          authType: node.data.authType || 'none',
          apiKeyHeader: node.data.apiKeyHeader || 'x-api-key',
          credentialId: node.data.credentialId || '',
          rateLimit: {
            enabled: node.data.rateLimit?.enabled || false,
            requestsPerMinute: node.data.rateLimit?.requestsPerMinute || 60
          },
          requestValidation: {
            enabled: node.data.requestValidation?.enabled || false,
            schema: JSON.stringify(node.data.requestValidation?.schema || {}, null, 2)
          },
          responseConfig: {
            successStatus: node.data.responseConfig?.successStatus || 200,
            successMessage: node.data.responseConfig?.successMessage || 'Workflow triggered successfully',
            errorStatus: node.data.responseConfig?.errorStatus || 500,
            errorMessage: node.data.responseConfig?.errorMessage || 'Failed to trigger workflow'
          }
        };
      case 'custom':
        return {
          name: node.data.name || `Custom Node`,
          description: node.data.description || 'Custom node with user-defined logic',
          code: node.data.code || '// Define your custom logic here\nfunction execute(input, context) {\n  // Process the input data\n  return {\n    result: "Custom processing complete",\n    data: input\n  };\n}',
          inputs: JSON.stringify(node.data.inputs || [{ name: 'input', type: 'any', required: true }], null, 2),
          outputs: JSON.stringify(node.data.outputs || [{ name: 'output', type: 'any' }], null, 2)
        };
      default:
        return {
          name: node.data.name || `Node`,
          description: node.data.description || ''
        };
    }
  });

  const [expandedMethod, setExpandedMethod] = useState<string | null>(null);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    let updatedData = { ...node.data };

    // Process the form data based on node type
    switch(node.type) {
      case 'input':
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description,
          schema: formData.schema,
          legacySchema: formData.legacySchema ? JSON.parse(formData.legacySchema) : undefined
        };
        break;

      case 'prompt':
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description,
          prompt: formData.prompt,
          provider: formData.provider,
          model: formData.model,
          credentialId: parseInt(formData.credentialId) || null,
          temperature: parseFloat(formData.temperature),
          maxTokens: parseInt(formData.maxTokens),
          outputFormat: formData.outputFormat
        };
        break;

      case 'agent':
        try {
          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            systemPrompt: formData.systemPrompt,
            provider: formData.provider,
            model: formData.model,
            credentialId: parseInt(formData.credentialId) || null,
            temperature: parseFloat(formData.temperature),
            maxTokens: parseInt(formData.maxTokens),
            outputFormat: formData.outputFormat,
            schema: formData.schema && typeof formData.schema === 'string' ? JSON.parse(formData.schema) : formData.schema
          };
        } catch (e) {
          alert('Invalid JSON schema format');
          return;
        }
        break;

      case 'api':
        try {
          const rateLimit = formData.rateLimit || { enabled: false, requestsPerMinute: 60 };
          const timeout = formData.timeout || { enabled: false, milliseconds: 30000 };
          const requestValidation = formData.requestValidation || { enabled: false, schema: '{}' };
          const responseFormat = formData.responseFormat || { type: 'json', schema: '{}' };

          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            url: formData.url,
            method: formData.method,
            headers: JSON.parse(formData.headers || '{}'),
            authType: formData.authType,
            apiKeyHeader: formData.apiKeyHeader,
            credentialId: formData.credentialId === 'none' ? null : parseInt(formData.credentialId),
            rateLimit: {
              enabled: rateLimit.enabled,
              requestsPerMinute: parseInt(String(rateLimit.requestsPerMinute)) || 60
            },
            timeout: {
              enabled: timeout.enabled,
              milliseconds: parseInt(String(timeout.milliseconds)) || 30000
            },
            requestValidation: {
              enabled: requestValidation.enabled,
              schema: JSON.parse(requestValidation.schema || '{}')
            },
            responseFormat: {
              type: responseFormat.type || 'json',
              schema: JSON.parse(responseFormat.schema || '{}')
            }
          };
        } catch (e) {
          alert('Invalid JSON format in configuration');
          return;
        }
        break;

      case 'api-trigger':
        try {
          const rateLimit = formData.rateLimit || { enabled: false, requestsPerMinute: 60 };
          const requestValidation = formData.requestValidation || { enabled: false, schema: '{}' };
          const responseConfig = formData.responseConfig || {
            successStatus: 200,
            successMessage: 'Workflow triggered successfully',
            errorStatus: 500,
            errorMessage: 'Failed to trigger workflow'
          };

          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            allowedMethods: formData.allowedMethods || ['POST'],
            authType: formData.authType,
            apiKeyHeader: formData.apiKeyHeader,
            credentialId: formData.credentialId === 'none' ? null : parseInt(formData.credentialId),
            rateLimit: {
              enabled: rateLimit.enabled,
              requestsPerMinute: parseInt(String(rateLimit.requestsPerMinute)) || 60
            },
            requestValidation: {
              enabled: requestValidation.enabled,
              schema: JSON.parse(requestValidation.schema || '{}')
            },
            responseConfig: {
              successStatus: parseInt(String(responseConfig.successStatus)) || 200,
              successMessage: responseConfig.successMessage || 'Workflow triggered successfully',
              errorStatus: parseInt(String(responseConfig.errorStatus)) || 500,
              errorMessage: responseConfig.errorMessage || 'Failed to trigger workflow'
            }
          };
        } catch (e) {
          alert('Invalid JSON format in configuration');
          return;
        }
        break;

      case 'custom':
        try {
          updatedData = {
            ...updatedData,
            name: formData.name,
            description: formData.description,
            code: formData.code,
            inputs: JSON.parse(formData.inputs || '[]'),
            outputs: JSON.parse(formData.outputs || '[]')
          };
        } catch (e) {
          alert('Invalid JSON format in inputs or outputs');
          return;
        }
        break;

      default:
        updatedData = {
          ...updatedData,
          name: formData.name,
          description: formData.description
        };
    }

    onUpdate(node.id, updatedData);
    onClose();
  };

  const getTitle = () => {
    switch(node.type) {
      case 'input': return 'Configure Input Node';
      case 'prompt': return 'Configure Prompt Node';
      case 'agent': return 'Configure Agent Node';
      case 'api': return 'Configure API Node';
      case 'api-trigger': return 'Configure API Trigger Node';
      case 'custom': return 'Configure Custom Node';
      default: return 'Configure Node';
    }
  };

  const getDescription = () => {
    switch(node.type) {
      case 'input': return 'Configure the input parameters and schema for this workflow entry point.';
      case 'prompt': return 'Set up the prompt template and AI model settings for text processing.';
      case 'agent': return 'Configure the AI agent with system prompts and behavior settings.';
      case 'api': return 'Configure API endpoint settings, authentication, and request parameters.';
      case 'api-trigger': return 'Configure webhook settings, authentication, and validation for incoming HTTP requests.';
      case 'custom': return 'Define custom logic, input parameters, and output structure for this node.';
      default: return 'Configure the settings and parameters for this workflow node.';
    }
  };

  const getExampleSchema = (allowedMethods: string[]) => {
    const hasGet = allowedMethods.includes('GET');
    const hasPost = allowedMethods.includes('POST') || allowedMethods.includes('PUT') || allowedMethods.includes('PATCH');
    const hasDelete = allowedMethods.includes('DELETE');

    if (hasGet && !hasPost && !hasDelete) {
      // GET only - query parameters
      return JSON.stringify({
        type: "object",
        properties: {
          id: { type: "string" },
          limit: { type: "number", minimum: 1, maximum: 100 },
          filter: { type: "string" }
        },
        required: ["id"]
      }, null, 2);
    } else if (hasPost && !hasGet && !hasDelete) {
      // POST/PUT/PATCH only - request body
      return JSON.stringify({
        type: "object",
        properties: {
          name: { type: "string", minLength: 1 },
          email: { type: "string", format: "email" },
          data: { type: "object" }
        },
        required: ["name", "email"]
      }, null, 2);
    } else if (hasDelete && !hasGet && !hasPost) {
      // DELETE only - query params or body
      return JSON.stringify({
        type: "object",
        properties: {
          id: { type: "string" },
          force: { type: "boolean" }
        },
        required: ["id"]
      }, null, 2);
    } else {
      // Mixed methods - generic schema
      return JSON.stringify({
        type: "object",
        properties: {
          id: { type: "string" },
          data: { type: "object" },
          action: { type: "string", enum: ["create", "update", "delete"] }
        },
        required: ["id"]
      }, null, 2);
    }
  };

  const getMethodExampleSchema = (method: string) => {
    switch (method) {
      case 'GET':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier" },
            limit: { type: "number", minimum: 1, maximum: 100, description: "Number of items to return" },
            offset: { type: "number", minimum: 0, description: "Number of items to skip" },
            filter: { type: "string", description: "Filter criteria" },
            sort: { type: "string", enum: ["asc", "desc"], description: "Sort order" }
          },
          required: ["id"]
        }, null, 2);

      case 'POST':
        return JSON.stringify({
          type: "object",
          properties: {
            name: { type: "string", minLength: 1, description: "Resource name" },
            email: { type: "string", format: "email", description: "Email address" },
            data: {
              type: "object",
              properties: {
                role: { type: "string", enum: ["admin", "user", "guest"] },
                preferences: { type: "object" }
              }
            }
          },
          required: ["name", "email"]
        }, null, 2);

      case 'PUT':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier" },
            name: { type: "string", minLength: 1, description: "Updated name" },
            email: { type: "string", format: "email", description: "Updated email" },
            status: { type: "string", enum: ["active", "inactive", "pending"] },
            data: { type: "object", description: "Complete resource data" }
          },
          required: ["id", "name", "email"]
        }, null, 2);

      case 'PATCH':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier" },
            name: { type: "string", minLength: 1, description: "Updated name (optional)" },
            email: { type: "string", format: "email", description: "Updated email (optional)" },
            status: { type: "string", enum: ["active", "inactive", "pending"] },
            metadata: { type: "object", description: "Partial updates to metadata" }
          },
          required: ["id"],
          additionalProperties: false
        }, null, 2);

      case 'DELETE':
        return JSON.stringify({
          type: "object",
          properties: {
            id: { type: "string", description: "Resource identifier to delete" },
            force: { type: "boolean", description: "Force deletion even if resource has dependencies" },
            reason: { type: "string", description: "Reason for deletion (optional)" }
          },
          required: ["id"]
        }, null, 2);

      default:
        return JSON.stringify({
          type: "object",
          properties: {
            action: { type: "string", description: "Action to perform" },
            data: { type: "object", description: "Request data" }
          },
          required: ["action"]
        }, null, 2);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="node-name">Node Name</Label>
              <Input
                id="node-name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                className="mt-1"
              />
            </div>

            <div className="col-span-2">
              <Label htmlFor="node-description">Description</Label>
              <Textarea
                id="node-description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                className="mt-1"
                rows={2}
              />
            </div>

            {node.type === 'input' && (
              <div className="col-span-2">
                <Label>Input Schema Configuration</Label>
                <Tabs defaultValue="visual" className="mt-2">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="visual">Visual Builder</TabsTrigger>
                    <TabsTrigger value="preview">Form Preview</TabsTrigger>
                    <TabsTrigger value="legacy">Legacy JSON</TabsTrigger>
                  </TabsList>

                  <TabsContent value="visual" className="mt-4">
                    <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
                      <SchemaBuilder
                        schema={formData.schema as InputSchema}
                        onChange={(schema) => handleChange('schema', schema)}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="preview" className="mt-4">
                    <div className="border rounded-lg p-4 max-h-96 overflow-y-auto">
                      <FormRenderer
                        schema={formData.schema as InputSchema}
                        values={{}}
                        onChange={() => {}}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="legacy" className="mt-4">
                    <CodeEditor
                      value={formData.legacySchema || '{}'}
                      onChange={(value) => handleChange('legacySchema', value)}
                      language="json"
                      height="200px"
                    />
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                      Legacy JSON schema format (for backward compatibility)
                    </p>
                  </TabsContent>
                </Tabs>
              </div>
            )}

            {node.type === 'prompt' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-provider">LLM Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => handleChange('provider', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="google">Google</SelectItem>
                      <SelectItem value="openrouter">OpenRouter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-model">LLM Model</Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => handleChange('model', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {formData.provider === 'google' ? (
                        <>
                          <SelectItem value="Gemini 2.5 Pro Preview">Gemini 2.5 Pro Preview</SelectItem>
                          <SelectItem value="Gemini 2.0 Flash">Gemini 2.0 Flash</SelectItem>
                          <SelectItem value="Gemini 2.0 Flash Lite">Gemini 2.0 Flash Lite</SelectItem>
                          <SelectItem value="Gemini 1.5 Pro">Gemini 1.5 Pro</SelectItem>
                          <SelectItem value="Gemini Pro">Gemini Pro (Legacy)</SelectItem>
                          <SelectItem value="Gemini Flash">Gemini Flash (Legacy)</SelectItem>
                        </>
                      ) : (
                        <>
                          <SelectItem value="DeepSeek Chat V3">DeepSeek Chat V3</SelectItem>
                          <SelectItem value="DeepSeek R1">DeepSeek R1</SelectItem>
                          <SelectItem value="DeepSeek R1T Chimera">DeepSeek R1T Chimera</SelectItem>
                          <SelectItem value="Qwen3 235B">Qwen3 235B</SelectItem>
                          <SelectItem value="Llama 4 Maverick">Llama 4 Maverick</SelectItem>
                          <SelectItem value="MAI DS R1">MAI DS R1</SelectItem>
                          <SelectItem value="Qwen 2.5 72B">Qwen 2.5 72B</SelectItem>
                          <SelectItem value="Qwen 2.5 32B">Qwen 2.5 32B</SelectItem>
                          <SelectItem value="Qwen 2.5 14B">Qwen 2.5 14B</SelectItem>
                          <SelectItem value="Gemma 2 9B">Gemma 2 9B</SelectItem>
                          <SelectItem value="Gemma 2 27B">Gemma 2 27B</SelectItem>
                          <SelectItem value="DeepSeek R1 Distill">DeepSeek R1 Distill</SelectItem>
                          <SelectItem value="DeepSeek Chat">DeepSeek Chat</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-credential">API Credential</Label>
                  <Select
                    value={formData.credentialId.toString()}
                    onValueChange={(value) => handleChange('credentialId', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select credential" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {credentials?.map(cred => (
                        <SelectItem key={cred.id} value={cred.id.toString()}>
                          {cred.name} ({cred.provider})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-prompt">Prompt Template</Label>
                  <Textarea
                    id="node-prompt"
                    value={formData.prompt}
                    onChange={(e) => handleChange('prompt', e.target.value)}
                    className="mt-1 font-mono"
                    rows={5}
                    placeholder="Create a detailed outline for an article about: {{query}}"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Use double curly braces for variables: {`{{variable_name}}`}
                  </p>
                </div>

                <div className="col-span-1">
                  <Label>Temperature</Label>
                  <div className="flex items-center mt-2">
                    <Slider
                      min={0}
                      max={1}
                      step={0.1}
                      value={[parseFloat(formData.temperature)]}
                      onValueChange={([value]) => handleChange('temperature', value)}
                      className="w-full"
                    />
                    <span className="ml-2 text-sm">{formData.temperature}</span>
                  </div>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-max-tokens">Max Tokens</Label>
                  <Input
                    id="node-max-tokens"
                    type="number"
                    value={formData.maxTokens}
                    onChange={(e) => handleChange('maxTokens', e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div className="col-span-2">
                  <Label>Output Format</Label>
                  <RadioGroup
                    value={formData.outputFormat}
                    onValueChange={(value) => handleChange('outputFormat', value)}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="text" id="text-output" />
                      <Label htmlFor="text-output">Text</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="json" id="json-output" />
                      <Label htmlFor="json-output">JSON</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="markdown" id="markdown-output" />
                      <Label htmlFor="markdown-output">Markdown</Label>
                    </div>
                  </RadioGroup>
                </div>
              </>
            )}

            {node.type === 'agent' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-provider">LLM Provider</Label>
                  <Select
                    value={formData.provider}
                    onValueChange={(value) => handleChange('provider', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="google">Google</SelectItem>
                      <SelectItem value="openrouter">OpenRouter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-model">LLM Model</Label>
                  <Select
                    value={formData.model}
                    onValueChange={(value) => handleChange('model', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {formData.provider === 'google' ? (
                        <>
                          <SelectItem value="Gemini 2.5 Pro Preview">Gemini 2.5 Pro Preview</SelectItem>
                          <SelectItem value="Gemini 2.0 Flash">Gemini 2.0 Flash</SelectItem>
                          <SelectItem value="Gemini 2.0 Flash Lite">Gemini 2.0 Flash Lite</SelectItem>
                          <SelectItem value="Gemini 1.5 Pro">Gemini 1.5 Pro</SelectItem>
                          <SelectItem value="Gemini Pro">Gemini Pro (Legacy)</SelectItem>
                          <SelectItem value="Gemini Flash">Gemini Flash (Legacy)</SelectItem>
                        </>
                      ) : (
                        <>
                          <SelectItem value="DeepSeek Chat V3">DeepSeek Chat V3</SelectItem>
                          <SelectItem value="DeepSeek R1">DeepSeek R1</SelectItem>
                          <SelectItem value="DeepSeek R1T Chimera">DeepSeek R1T Chimera</SelectItem>
                          <SelectItem value="Qwen3 235B">Qwen3 235B</SelectItem>
                          <SelectItem value="Llama 4 Maverick">Llama 4 Maverick</SelectItem>
                          <SelectItem value="MAI DS R1">MAI DS R1</SelectItem>
                          <SelectItem value="Qwen 2.5 72B">Qwen 2.5 72B</SelectItem>
                          <SelectItem value="Qwen 2.5 32B">Qwen 2.5 32B</SelectItem>
                          <SelectItem value="Qwen 2.5 14B">Qwen 2.5 14B</SelectItem>
                          <SelectItem value="Gemma 2 9B">Gemma 2 9B</SelectItem>
                          <SelectItem value="Gemma 2 27B">Gemma 2 27B</SelectItem>
                          <SelectItem value="DeepSeek R1 Distill">DeepSeek R1 Distill</SelectItem>
                          <SelectItem value="DeepSeek Chat">DeepSeek Chat</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-credential">API Credential</Label>
                  <Select
                    value={formData.credentialId.toString()}
                    onValueChange={(value) => handleChange('credentialId', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select credential" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {credentials?.map(cred => (
                        <SelectItem key={cred.id} value={cred.id.toString()}>
                          {cred.name} ({cred.provider})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-system-prompt">System Prompt</Label>
                  <Textarea
                    id="node-system-prompt"
                    value={formData.systemPrompt}
                    onChange={(e) => handleChange('systemPrompt', e.target.value)}
                    className="mt-1 font-mono"
                    rows={5}
                    placeholder="You are a professional content writer. Create a well-structured article based on this outline."
                  />
                </div>

                <div className="col-span-1">
                  <Label>Temperature</Label>
                  <div className="flex items-center mt-2">
                    <Slider
                      min={0}
                      max={1}
                      step={0.1}
                      value={[parseFloat(formData.temperature)]}
                      onValueChange={([value]) => handleChange('temperature', value)}
                      className="w-full"
                    />
                    <span className="ml-2 text-sm">{formData.temperature}</span>
                  </div>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-max-tokens">Max Tokens</Label>
                  <Input
                    id="node-max-tokens"
                    type="number"
                    value={formData.maxTokens}
                    onChange={(e) => handleChange('maxTokens', e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div className="col-span-2">
                  <Label>Output Format</Label>
                  <RadioGroup
                    value={formData.outputFormat}
                    onValueChange={(value) => handleChange('outputFormat', value)}
                    className="flex space-x-4 mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="text" id="text-output" />
                      <Label htmlFor="text-output">Text</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="json" id="json-output" />
                      <Label htmlFor="json-output">JSON</Label>
                    </div>
                  </RadioGroup>
                </div>

                {formData.outputFormat === 'json' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-schema">JSON Schema (optional)</Label>
                    <CodeEditor
                      value={typeof formData.schema === 'string' ? formData.schema : '{}'}
                      onChange={(value) => handleChange('schema', value)}
                      language="json"
                      height="150px"
                      className="mt-1"
                    />
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                      Define the expected structure of the JSON output
                    </p>
                  </div>
                )}
              </>
            )}

            {node.type === 'api' && (
              <>
                <div className="col-span-1">
                  <Label htmlFor="node-url">API Endpoint</Label>
                  <Input
                    id="node-url"
                    value={formData.url}
                    onChange={(e) => handleChange('url', e.target.value)}
                    className="mt-1"
                    placeholder="https://api.example.com/endpoint"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-method">HTTP Method</Label>
                  <Select
                    value={formData.method}
                    onValueChange={(value) => handleChange('method', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GET">GET</SelectItem>
                      <SelectItem value="POST">POST</SelectItem>
                      <SelectItem value="PUT">PUT</SelectItem>
                      <SelectItem value="DELETE">DELETE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="col-span-2">
                  <Label htmlFor="node-headers">Headers (JSON)</Label>
                  <CodeEditor
                    value={formData.headers || '{}'}
                    onChange={(value) => handleChange('headers', value)}
                    language="json"
                    height="100px"
                    className="mt-1"
                  />
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-auth-type">Authentication</Label>
                  <Select
                    value={formData.authType}
                    onValueChange={(value) => handleChange('authType', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select auth type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="apiKey">API Key</SelectItem>
                      <SelectItem value="bearer">Bearer Token</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.authType === 'apiKey' && (
                  <div className="col-span-1">
                    <Label htmlFor="node-api-key-header">API Key Header</Label>
                    <Input
                      id="node-api-key-header"
                      value={formData.apiKeyHeader}
                      onChange={(e) => handleChange('apiKeyHeader', e.target.value)}
                      className="mt-1"
                      placeholder="x-api-key"
                    />
                  </div>
                )}

                {formData.authType !== 'none' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-credential">Credential</Label>
                    <Select
                      value={formData.credentialId.toString()}
                      onValueChange={(value) => handleChange('credentialId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select credential" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {credentials?.map(cred => (
                          <SelectItem key={cred.id} value={cred.id.toString()}>
                            {cred.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </>
            )}

            {node.type === 'api-trigger' && (
              <>
                <div className="col-span-2">
                  <Label>Allowed HTTP Methods</Label>
                  <div className="mt-2 space-y-2">
                    {['GET', 'POST', 'PUT', 'DELETE'].map((method) => (
                      <label key={method} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={formData.allowedMethods?.includes(method) || false}
                          onChange={(e) => {
                            const currentMethods = formData.allowedMethods || [];
                            if (e.target.checked) {
                              handleChange('allowedMethods', [...currentMethods, method]);
                            } else {
                              handleChange('allowedMethods', currentMethods.filter((m: string) => m !== method));
                            }
                          }}
                          className="rounded"
                        />
                        <span className="text-sm">{method}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-auth-type">Authentication</Label>
                  <Select
                    value={formData.authType}
                    onValueChange={(value) => handleChange('authType', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select auth type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="apiKey">API Key</SelectItem>
                      <SelectItem value="bearer">Bearer Token</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {formData.authType === 'apiKey' && (
                  <div className="col-span-1">
                    <Label htmlFor="node-api-key-header">API Key Header</Label>
                    <Input
                      id="node-api-key-header"
                      value={formData.apiKeyHeader}
                      onChange={(e) => handleChange('apiKeyHeader', e.target.value)}
                      className="mt-1"
                      placeholder="x-api-key"
                    />
                  </div>
                )}

                {formData.authType !== 'none' && (
                  <div className="col-span-2">
                    <Label htmlFor="node-credential">Credential</Label>
                    <Select
                      value={formData.credentialId.toString()}
                      onValueChange={(value) => handleChange('credentialId', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select credential" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {credentials?.map(cred => (
                          <SelectItem key={cred.id} value={cred.id.toString()}>
                            {cred.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="col-span-2">
                  <Label>Rate Limiting</Label>
                  <div className="mt-2 space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.rateLimit?.enabled || false}
                        onChange={(e) => handleChange('rateLimit', {
                          ...formData.rateLimit,
                          enabled: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Enable rate limiting</span>
                    </label>
                    {formData.rateLimit?.enabled && (
                      <div>
                        <Label htmlFor="rate-limit-requests">Requests per minute</Label>
                        <Input
                          id="rate-limit-requests"
                          type="number"
                          value={formData.rateLimit?.requestsPerMinute || 60}
                          onChange={(e) => handleChange('rateLimit', {
                            ...formData.rateLimit,
                            requestsPerMinute: parseInt(e.target.value)
                          })}
                          className="mt-1"
                          min="1"
                          max="1000"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="col-span-2">
                  <Label>Request Validation</Label>
                  <div className="mt-2 space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.requestValidation?.enabled || false}
                        onChange={(e) => handleChange('requestValidation', {
                          ...formData.requestValidation,
                          enabled: e.target.checked
                        })}
                        className="rounded"
                      />
                      <span className="text-sm">Enable request validation</span>
                    </label>
                    {formData.requestValidation?.enabled && (
                      <div>
                        <Label htmlFor="validation-schema">JSON Schema</Label>
                        <CodeEditor
                          value={formData.requestValidation?.schema || '{}'}
                          onChange={(value) => handleChange('requestValidation', {
                            ...formData.requestValidation,
                            schema: value
                          })}
                          language="json"
                          height="150px"
                          className="mt-1"
                        />
                        <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          <p className="mb-2">Define JSON schema to validate incoming request data.</p>

                          <div className="space-y-2">
                            {/* GET Method Accordion */}
                            <div className="border border-neutral-200 dark:border-neutral-700 rounded">
                              <button
                                type="button"
                                className={`w-full px-3 py-2 text-left text-xs font-medium flex items-center justify-between ${
                                  formData.allowedMethods?.includes('GET')
                                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                                    : 'bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400'
                                }`}
                                onClick={() => setExpandedMethod(expandedMethod === 'GET' ? null : 'GET')}
                              >
                                <span>GET - Query Parameters</span>
                                <span className={`transform transition-transform ${expandedMethod === 'GET' ? 'rotate-180' : ''}`}>▼</span>
                              </button>
                              {(expandedMethod === 'GET' || formData.allowedMethods?.includes('GET')) && (
                                <div className="p-3 border-t border-neutral-200 dark:border-neutral-700">
                                  <p className="text-xs mb-2">Validates query parameters for resource filtering and pagination:</p>
                                  <pre className="text-xs bg-neutral-100 dark:bg-neutral-900 p-2 rounded overflow-x-auto">
{getMethodExampleSchema('GET')}
                                  </pre>
                                  <p className="text-xs mt-1 text-neutral-500">Example: <code>?id=user123&limit=10&filter=active</code></p>
                                </div>
                              )}
                            </div>

                            {/* POST Method Accordion */}
                            <div className="border border-neutral-200 dark:border-neutral-700 rounded">
                              <button
                                type="button"
                                className={`w-full px-3 py-2 text-left text-xs font-medium flex items-center justify-between ${
                                  formData.allowedMethods?.includes('POST')
                                    ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                                    : 'bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400'
                                }`}
                                onClick={() => setExpandedMethod(expandedMethod === 'POST' ? null : 'POST')}
                              >
                                <span>POST - Request Body</span>
                                <span className={`transform transition-transform ${expandedMethod === 'POST' ? 'rotate-180' : ''}`}>▼</span>
                              </button>
                              {(expandedMethod === 'POST' || formData.allowedMethods?.includes('POST')) && (
                                <div className="p-3 border-t border-neutral-200 dark:border-neutral-700">
                                  <p className="text-xs mb-2">Validates request body for creating new resources:</p>
                                  <pre className="text-xs bg-neutral-100 dark:bg-neutral-900 p-2 rounded overflow-x-auto">
{getMethodExampleSchema('POST')}
                                  </pre>
                                  <p className="text-xs mt-1 text-neutral-500">Fallback: Query parameters if no body present</p>
                                </div>
                              )}
                            </div>

                            {/* PUT Method Accordion */}
                            <div className="border border-neutral-200 dark:border-neutral-700 rounded">
                              <button
                                type="button"
                                className={`w-full px-3 py-2 text-left text-xs font-medium flex items-center justify-between ${
                                  formData.allowedMethods?.includes('PUT')
                                    ? 'bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300'
                                    : 'bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400'
                                }`}
                                onClick={() => setExpandedMethod(expandedMethod === 'PUT' ? null : 'PUT')}
                              >
                                <span>PUT - Request Body</span>
                                <span className={`transform transition-transform ${expandedMethod === 'PUT' ? 'rotate-180' : ''}`}>▼</span>
                              </button>
                              {(expandedMethod === 'PUT' || formData.allowedMethods?.includes('PUT')) && (
                                <div className="p-3 border-t border-neutral-200 dark:border-neutral-700">
                                  <p className="text-xs mb-2">Validates request body for updating entire resources:</p>
                                  <pre className="text-xs bg-neutral-100 dark:bg-neutral-900 p-2 rounded overflow-x-auto">
{getMethodExampleSchema('PUT')}
                                  </pre>
                                  <p className="text-xs mt-1 text-neutral-500">Fallback: Query parameters if no body present</p>
                                </div>
                              )}
                            </div>

                            {/* PATCH Method Accordion */}
                            <div className="border border-neutral-200 dark:border-neutral-700 rounded">
                              <button
                                type="button"
                                className={`w-full px-3 py-2 text-left text-xs font-medium flex items-center justify-between ${
                                  formData.allowedMethods?.includes('PATCH')
                                    ? 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300'
                                    : 'bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400'
                                }`}
                                onClick={() => setExpandedMethod(expandedMethod === 'PATCH' ? null : 'PATCH')}
                              >
                                <span>PATCH - Request Body</span>
                                <span className={`transform transition-transform ${expandedMethod === 'PATCH' ? 'rotate-180' : ''}`}>▼</span>
                              </button>
                              {(expandedMethod === 'PATCH' || formData.allowedMethods?.includes('PATCH')) && (
                                <div className="p-3 border-t border-neutral-200 dark:border-neutral-700">
                                  <p className="text-xs mb-2">Validates request body for partial resource updates:</p>
                                  <pre className="text-xs bg-neutral-100 dark:bg-neutral-900 p-2 rounded overflow-x-auto">
{getMethodExampleSchema('PATCH')}
                                  </pre>
                                  <p className="text-xs mt-1 text-neutral-500">Fallback: Query parameters if no body present</p>
                                </div>
                              )}
                            </div>

                            {/* DELETE Method Accordion */}
                            <div className="border border-neutral-200 dark:border-neutral-700 rounded">
                              <button
                                type="button"
                                className={`w-full px-3 py-2 text-left text-xs font-medium flex items-center justify-between ${
                                  formData.allowedMethods?.includes('DELETE')
                                    ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                                    : 'bg-neutral-50 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400'
                                }`}
                                onClick={() => setExpandedMethod(expandedMethod === 'DELETE' ? null : 'DELETE')}
                              >
                                <span>DELETE - Body or Query Parameters</span>
                                <span className={`transform transition-transform ${expandedMethod === 'DELETE' ? 'rotate-180' : ''}`}>▼</span>
                              </button>
                              {(expandedMethod === 'DELETE' || formData.allowedMethods?.includes('DELETE')) && (
                                <div className="p-3 border-t border-neutral-200 dark:border-neutral-700">
                                  <p className="text-xs mb-2">Validates request body or query parameters for resource deletion:</p>
                                  <pre className="text-xs bg-neutral-100 dark:bg-neutral-900 p-2 rounded overflow-x-auto">
{getMethodExampleSchema('DELETE')}
                                  </pre>
                                  <p className="text-xs mt-1 text-neutral-500">Example: <code>?id=user123&force=true</code> or JSON body</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {node.type === 'custom' && (
              <>
                <div className="col-span-2">
                  <Label htmlFor="node-code">Custom Logic</Label>
                  <CodeEditor
                    value={formData.code || ''}
                    onChange={(value) => handleChange('code', value)}
                    language="javascript"
                    height="300px"
                    className="mt-1"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Define your custom node logic. The function should accept (input, context) and return the output.
                  </p>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-inputs">Input Parameters (JSON)</Label>
                  <CodeEditor
                    value={formData.inputs || '[]'}
                    onChange={(value) => handleChange('inputs', value)}
                    language="json"
                    height="150px"
                    className="mt-1"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Define input parameters: {`[{"name": "param", "type": "string", "required": true}]`}
                  </p>
                </div>

                <div className="col-span-1">
                  <Label htmlFor="node-outputs">Output Parameters (JSON)</Label>
                  <CodeEditor
                    value={formData.outputs || '[]'}
                    onChange={(value) => handleChange('outputs', value)}
                    language="json"
                    height="150px"
                    className="mt-1"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                    Define output parameters: {`[{"name": "result", "type": "string"}]`}
                  </p>
                </div>
              </>
            )}

            {formData.credentialId && (
              <div className="col-span-2 pt-2">
                <div className="flex items-center text-neutral-600 dark:text-neutral-400 text-sm">
                  <Key className="w-4 h-4 mr-2 text-primary" />
                  <span>Using credential: <strong>
                    {credentials?.find(c => c.id === parseInt(formData.credentialId))?.name || 'Unknown'}
                  </strong></span>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NodeConfigModal;
