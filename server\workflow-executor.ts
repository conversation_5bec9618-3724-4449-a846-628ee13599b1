import { storage } from "./storage";
import { aiService } from "./ai-service";
import { WorkflowLogger, LoggingService, LogCategory } from "./logging-service";
import type { Workflow, WorkflowRun } from "@shared/schema";
import axios, { AxiosError } from 'axios';
import rateLimit from 'express-rate-limit';
import { validate, ValidationError } from 'jsonschema';

export interface NodeExecutor {
  execute(nodeData: any, input: any, context: ExecutionContext): Promise<any>;
}

export interface ExecutionContext {
  workflowRun: WorkflowRun;
  workflow: Workflow;
  nodeOutputs: Map<string, any>;
  credentials: Map<number, any>;
  logger: WorkflowLogger;
}

export class WorkflowExecutor {
  private nodeExecutors: Map<string, NodeExecutor>;

  constructor() {
    this.nodeExecutors = new Map();
    this.registerNodeExecutors();
  }

  private registerNodeExecutors() {
    this.nodeExecutors.set('input', new InputNodeExecutor());
    this.nodeExecutors.set('prompt', new PromptNodeExecutor());
    this.nodeExecutors.set('agent', new AgentNodeExecutor());
    this.nodeExecutors.set('api', new ApiNodeExecutor());
    this.nodeExecutors.set('api-trigger', new ApiTriggerNodeExecutor());
    this.nodeExecutors.set('custom', new CustomNodeExecutor());
  }

  async executeWorkflow(workflowRunId: number): Promise<void> {
    const startTime = Date.now();
    let logger: WorkflowLogger | undefined;

    try {
      const workflowRun = await storage.getWorkflowRun(workflowRunId);
      if (!workflowRun) {
        throw new Error(`Workflow run ${workflowRunId} not found`);
      }

      const workflow = await storage.getWorkflow(workflowRun.workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${workflowRun.workflowId} not found`);
      }

      // Create logger
      logger = LoggingService.createWorkflowLogger(workflowRunId, workflow.id);

      await logger.info(`Starting workflow execution: ${workflow.name}`, {
        workflowId: workflow.id,
        workflowName: workflow.name,
        triggerType: workflowRun.triggerType,
        input: workflowRun.input
      }, LogCategory.EXECUTION);

      // Update status to running
      const workflowNodes = workflow.nodes as Record<string, any>;
      await storage.updateWorkflowRun(workflowRunId, {
        status: 'running',
        totalNodes: Object.keys(workflowNodes).length,
        completedNodes: 0,
        failedNodes: 0
      });

      // Create execution context
      const context: ExecutionContext = {
        workflowRun,
        workflow,
        nodeOutputs: new Map(),
        credentials: new Map(),
        logger
      };

      // Load credentials
      await this.loadCredentials(context);

      // Get execution order
      const executionOrder = this.getExecutionOrder(workflow);
      await logger.info(`Execution order determined`, {
        nodeCount: executionOrder.length,
        executionOrder
      }, LogCategory.EXECUTION);

      logger.startPerformanceTimer('workflow_execution');

      let completedNodes = 0;
      let failedNodes = 0;

      // Execute nodes in order
      for (const nodeId of executionOrder) {
        try {
          await this.executeNode(nodeId, context);
          completedNodes++;
          await storage.updateWorkflowRun(workflowRunId, { completedNodes });
        } catch (error) {
          failedNodes++;
          await storage.updateWorkflowRun(workflowRunId, { failedNodes });
          throw error; // Re-throw to stop execution
        }
      }

      await logger.endPerformanceTimer('workflow_execution', {
        completedNodes,
        failedNodes,
        totalNodes: executionOrder.length
      });

      // Mark workflow as completed
      const endTime = Date.now();
      const executionDuration = endTime - startTime;

      await storage.updateWorkflowRun(workflowRunId, {
        status: 'completed',
        endTime: new Date(),
        executionDuration,
        completedNodes,
        failedNodes
      });

      await logger.info(`Workflow execution completed successfully`, {
        executionDuration,
        completedNodes,
        failedNodes,
        totalNodes: executionOrder.length
      }, LogCategory.EXECUTION);

    } catch (error) {
      const endTime = Date.now();
      const executionDuration = endTime - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const stackTrace = error instanceof Error ? error.stack : undefined;

      if (logger) {
        await logger.error(`Workflow execution failed: ${errorMessage}`, error as Error, {
          executionDuration,
          failurePoint: 'workflow_execution'
        }, LogCategory.ERROR);
      }

      await storage.updateWorkflowRun(workflowRunId, {
        status: 'failed',
        endTime: new Date(),
        executionDuration,
        errorMessage,
        stackTrace
      });

      throw error;
    }
  }

  private async loadCredentials(context: ExecutionContext): Promise<void> {
    const credentials = await storage.getCredentials();
    for (const credential of credentials) {
      context.credentials.set(credential.id, credential);
    }
  }

  private getExecutionOrder(workflow: Workflow): string[] {
    const nodes = Object.keys(workflow.nodes as Record<string, any>);
    const edges = Object.values(workflow.edges as Record<string, any>);
    const visited = new Set<string>();
    const order: string[] = [];

    // Simple topological sort
    const visit = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      // Visit dependencies first
      const incomingEdges = edges.filter((edge: any) => edge.target === nodeId);
      for (const edge of incomingEdges) {
        visit((edge as any).source);
      }

      order.push(nodeId);
    };

    for (const nodeId of nodes) {
      visit(nodeId);
    }

    return order;
  }

  private async executeNode(nodeId: string, context: ExecutionContext): Promise<void> {
    const startTime = Date.now();
    const node = (context.workflow.nodes as Record<string, any>)[nodeId];
    if (!node) {
      throw new Error(`Node ${nodeId} not found in workflow`);
    }

    const nodeLogger = context.logger.createNodeLogger(0, nodeId, node.type); // Will update with actual nodeRunId

    await nodeLogger.info(`Starting node execution: ${nodeId}`, {
      nodeType: node.type,
      nodeName: node.data?.name || nodeId,
      nodeConfiguration: node.data
    }, LogCategory.EXECUTION);

    // Create node run
    const nodeInput = this.getNodeInput(nodeId, context);
    const nodeRun = await storage.createNodeRun({
      workflowRunId: context.workflowRun.id,
      nodeId,
      nodeType: node.type,
      nodeName: node.data?.name || nodeId,
      status: 'running',
      input: nodeInput,
      output: {}
    });

    // Update logger with actual node run ID
    const actualNodeLogger = context.logger.createNodeLogger(nodeRun.id, nodeId, node.type);

    try {
      await actualNodeLogger.debug(`Node input data`, {
        input: nodeInput,
        inputSize: JSON.stringify(nodeInput).length
      }, LogCategory.EXECUTION);

      // Get node executor
      const executor = this.nodeExecutors.get(node.type);
      if (!executor) {
        throw new Error(`No executor found for node type: ${node.type}`);
      }

      actualNodeLogger.startPerformanceTimer(`node_${nodeId}_execution`);

      // Execute node
      const output = await executor.execute(node.data, nodeInput, context);

      await actualNodeLogger.endPerformanceTimer(`node_${nodeId}_execution`, {
        outputSize: JSON.stringify(output).length,
        nodeType: node.type
      });

      await actualNodeLogger.debug(`Node output data`, {
        output,
        outputSize: JSON.stringify(output).length
      }, LogCategory.EXECUTION);

      // Store output
      context.nodeOutputs.set(nodeId, output);

      // Calculate execution metrics
      const endTime = Date.now();
      const executionDuration = endTime - startTime;

      // Update node run
      await storage.updateNodeRun(nodeRun.id, {
        status: 'completed',
        output,
        endTime: new Date(),
        executionDuration
      });

      await actualNodeLogger.info(`Node execution completed successfully`, {
        executionDuration,
        outputKeys: Object.keys(output || {}),
        success: true
      }, LogCategory.EXECUTION);

    } catch (error) {
      const endTime = Date.now();
      const executionDuration = endTime - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const stackTrace = error instanceof Error ? error.stack : undefined;

      await actualNodeLogger.error(`Node execution failed: ${errorMessage}`, error as Error, {
        executionDuration,
        nodeType: node.type,
        nodeConfiguration: node.data,
        input: nodeInput
      }, LogCategory.ERROR);

      await storage.updateNodeRun(nodeRun.id, {
        status: 'failed',
        error: errorMessage,
        stackTrace,
        endTime: new Date(),
        executionDuration
      });

      throw error;
    }
  }

  private getNodeInput(nodeId: string, context: ExecutionContext): any {
    const edges = Object.values(context.workflow.edges as Record<string, any>);
    const incomingEdges = edges.filter((edge: any) => edge.target === nodeId);

    if (incomingEdges.length === 0) {
      // Input node - use workflow run input
      return context.workflowRun.input;
    }

    // Collect outputs from source nodes
    const input: any = {};
    for (const edge of incomingEdges) {
      const sourceOutput = context.nodeOutputs.get((edge as any).source);
      if (sourceOutput !== undefined) {
        Object.assign(input, sourceOutput);
      }
    }

    return input;
  }
}

// Node Executors
class InputNodeExecutor implements NodeExecutor {
  async execute(_nodeData: any, input: any, _context: ExecutionContext): Promise<any> {
    // Input nodes just pass through the input data
    return input;
  }
}

class PromptNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext): Promise<any> {
    // Log node data for debugging
    await context.logger.debug(`PromptNodeExecutor: nodeData`, {
      credentialId: nodeData.credentialId,
      provider: nodeData.provider,
      model: nodeData.model,
      availableCredentials: Array.from(context.credentials.keys())
    }, LogCategory.EXECUTION);

    // Get credential
    const credential = context.credentials.get(nodeData.credentialId);
    if (!credential) {
      const availableCredentials = Array.from(context.credentials.keys());
      throw new Error(`Credential ${nodeData.credentialId} not found. Available credentials: ${availableCredentials.join(', ')}`);
    }

    // Log credential info (without API key)
    await context.logger.debug(`PromptNodeExecutor: Using credential`, {
      credentialId: credential.id,
      credentialName: credential.name,
      credentialProvider: credential.provider,
      hasApiKey: !!credential.apiKey
    }, LogCategory.EXECUTION);

    // Replace template variables in prompt
    let prompt = nodeData.prompt || '';
    for (const [key, value] of Object.entries(input)) {
      prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    // Use the AI service for real AI generation
    const result = await aiService.generateResponse({
      provider: nodeData.provider,
      model: nodeData.model,
      prompt,
      maxTokens: nodeData.maxTokens,
      temperature: nodeData.temperature,
      outputFormat: nodeData.outputFormat,
      schema: nodeData.schema
    }, credential);

    return result;
  }
}

class AgentNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext): Promise<any> {
    // Get credential
    const credential = context.credentials.get(nodeData.credentialId);
    if (!credential) {
      throw new Error(`Credential ${nodeData.credentialId} not found`);
    }

    // Create prompt from input data
    const prompt = `Process this input data: ${JSON.stringify(input)}`;

    // Use the AI service for real AI generation
    const result = await aiService.generateResponse({
      provider: nodeData.provider,
      model: nodeData.model,
      prompt,
      systemPrompt: nodeData.systemPrompt,
      maxTokens: nodeData.maxTokens,
      temperature: nodeData.temperature,
      outputFormat: nodeData.outputFormat,
      schema: nodeData.schema
    }, credential);

    return result;
  }
}

class ApiNodeExecutor implements NodeExecutor {
  private rateLimiters: Map<string, any> = new Map();

  private getRateLimiter(nodeId: string, requestsPerMinute: number) {
    if (!this.rateLimiters.has(nodeId)) {
      this.rateLimiters.set(nodeId, rateLimit({
        windowMs: 60 * 1000, // 1 minute
        max: requestsPerMinute,
        message: 'Rate limit exceeded'
      }));
    }
    return this.rateLimiters.get(nodeId);
  }

  private validateRequest(input: any, schema: Record<string, any>): void {
    const validation = validate(input, schema);
    if (!validation.valid) {
      throw new Error(`Request validation failed: ${validation.errors.map((e: ValidationError) => e.message).join(', ')}`);
    }
  }

  async execute(nodeData: any, input: any, context: ExecutionContext): Promise<any> {
    try {
      // Apply rate limiting if enabled
      if (nodeData.rateLimit?.enabled) {
        const limiter = this.getRateLimiter(context.workflowRun.id.toString(), nodeData.rateLimit.requestsPerMinute);
        await new Promise((resolve, reject) => {
          limiter({}, {}, (err: Error | null) => {
            if (err) reject(err);
            else resolve(undefined);
          });
        });
      }

      // Validate request if enabled
      if (nodeData.requestValidation?.enabled) {
        this.validateRequest(input, nodeData.requestValidation.schema);
      }

      // Configure axios with timeout if enabled
      const axiosConfig: any = {
        method: nodeData.method || 'POST',
        url: nodeData.url,
        headers: nodeData.headers || {},
      };

      if (nodeData.timeout?.enabled) {
        axiosConfig.timeout = nodeData.timeout.milliseconds;
      }

      // Add request body for non-GET requests
      if (nodeData.method !== 'GET') {
        axiosConfig.data = input;
      }

      // Add authentication if configured
      if (nodeData.authType === 'apiKey' && nodeData.credentialId) {
        const credential = context.credentials.get(nodeData.credentialId);
        if (credential) {
          const headerName = nodeData.apiKeyHeader || 'x-api-key';
          axiosConfig.headers[headerName] = credential.apiKey;
        }
      }

      // Make the HTTP request
      const response = await axios(axiosConfig);

      // Format response based on configuration
      let formattedResponse = response.data;
      if (nodeData.responseFormat?.type === 'text') {
        formattedResponse = typeof response.data === 'string' ? response.data : JSON.stringify(response.data);
      } else if (nodeData.responseFormat?.type === 'binary') {
        formattedResponse = response.data;
      }

      // Validate response schema if configured
      if (nodeData.responseFormat?.schema) {
        this.validateRequest(formattedResponse, nodeData.responseFormat.schema);
      }

      return {
        status: 'success',
        statusCode: response.status,
        headers: response.headers,
        data: formattedResponse
      };
    } catch (error: unknown) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        throw new Error(`API request failed: ${axiosError.message}`);
      }
      throw error;
    }
  }
}

class ApiTriggerNodeExecutor implements NodeExecutor {
  private rateLimiters: Map<string, any> = new Map();

  private getRateLimiter(nodeId: string, requestsPerMinute: number) {
    if (!this.rateLimiters.has(nodeId)) {
      this.rateLimiters.set(nodeId, rateLimit({
        windowMs: 60 * 1000, // 1 minute
        max: requestsPerMinute,
        message: 'Rate limit exceeded'
      }));
    }
    return this.rateLimiters.get(nodeId);
  }

  private validateRequest(input: any, schema: Record<string, any>): void {
    const validation = validate(input, schema);
    if (!validation.valid) {
      throw new Error(`Request validation failed: ${validation.errors.map((e: ValidationError) => e.message).join(', ')}`);
    }
  }

  private getDataToValidate(input: any): any {
    const method = input.method?.toUpperCase();

    switch (method) {
      case 'GET':
        // For GET requests, validate query parameters
        return input.query || {};

      case 'POST':
      case 'PUT':
      case 'PATCH':
        // For POST/PUT/PATCH requests, validate request body
        // Fall back to query params if no body is present
        return input.body || input.query || {};

      case 'DELETE':
        // For DELETE requests, typically validate query params or path params
        // Some DELETE requests may have a body, so check both
        return input.body || input.query || {};

      default:
        // For unknown methods, validate the entire input structure
        return {
          method: input.method,
          headers: input.headers,
          query: input.query,
          body: input.body,
          params: input.params
        };
    }
  }

  private getValidationTarget(method: string): string {
    const upperMethod = method?.toUpperCase();

    switch (upperMethod) {
      case 'GET':
        return 'query_parameters';
      case 'POST':
      case 'PUT':
      case 'PATCH':
        return 'request_body';
      case 'DELETE':
        return 'request_body_or_query_parameters';
      default:
        return 'entire_request';
    }
  }

  async execute(nodeData: any, input: any, context: ExecutionContext): Promise<any> {
    try {
      // API trigger nodes receive webhook data and pass it through
      // The input should contain the webhook request data: method, headers, query, body, params

      // Validate the HTTP method if allowedMethods is configured
      if (nodeData.allowedMethods && nodeData.allowedMethods.length > 0) {
        const requestMethod = input.method?.toUpperCase();
        if (!nodeData.allowedMethods.map((m: string) => m.toUpperCase()).includes(requestMethod)) {
          throw new Error(`HTTP method ${requestMethod} not allowed. Allowed methods: ${nodeData.allowedMethods.join(', ')}`);
        }
      }

      // Apply rate limiting if enabled
      if (nodeData.rateLimit?.enabled) {
        const limiter = this.getRateLimiter(context.workflowRun.id.toString(), nodeData.rateLimit.requestsPerMinute);
        await new Promise((resolve, reject) => {
          limiter({}, {}, (err: Error | null) => {
            if (err) reject(err);
            else resolve(undefined);
          });
        });
      }

      // Validate request if enabled
      if (nodeData.requestValidation?.enabled) {
        // Validate data based on HTTP method conventions
        const dataToValidate = this.getDataToValidate(input);
        this.validateRequest(dataToValidate, nodeData.requestValidation.schema);
      }

      // Extract and structure the webhook data for downstream nodes
      const webhookData = {
        method: input.method,
        headers: input.headers,
        query: input.query,
        body: input.body,
        params: input.params,
        timestamp: new Date().toISOString(),
        triggerNodeId: context.workflowRun.id,
        // Add validation metadata
        validation: {
          enabled: nodeData.requestValidation?.enabled || false,
          validatedData: nodeData.requestValidation?.enabled ? this.getDataToValidate(input) : null,
          validationTarget: this.getValidationTarget(input.method)
        }
      };

      return webhookData;
    } catch (error) {
      throw new Error(`API trigger node execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

class CustomNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext): Promise<any> {
    try {
      // Get the custom code
      const code = nodeData.code || '';

      // Create a safe execution environment
      const customFunction = new Function('input', 'context', `
        ${code}

        // If the code doesn't define an execute function, create a default one
        if (typeof execute !== 'function') {
          function execute(input, context) {
            return { result: 'Custom node executed', data: input };
          }
        }

        return execute(input, context);
      `);

      // Execute the custom function
      const result = await customFunction(input, {
        workflowId: context.workflow.id,
        runId: context.workflowRun.id,
        nodeOutputs: Object.fromEntries(context.nodeOutputs)
      });

      return result;
    } catch (error) {
      throw new Error(`Custom node execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}

export const workflowExecutor = new WorkflowExecutor();
