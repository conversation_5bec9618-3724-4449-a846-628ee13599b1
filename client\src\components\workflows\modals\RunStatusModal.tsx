import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Workflow, WorkflowRun, NodeRun, LogEntry } from '@/types/workflow';
import { useQuery } from '@tanstack/react-query';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import {
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  Download,
  ArrowRight,
  MessageSquare,
  Bot,
  Code,
  FileText,
  Bug,
  Info,
  AlertTriangle,
  Zap
} from 'lucide-react';
import { format } from 'date-fns';

interface RunStatusModalProps {
  workflow: Workflow;
  runId?: number;
  onClose: () => void;
}

const RunStatusModal: React.FC<RunStatusModalProps> = ({ workflow, runId, onClose }) => {
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Get the latest workflow run if no runId is provided
  const { data: workflowRuns, isLoading: isRunsLoading } = useQuery<WorkflowRun[]>({
    queryKey: [`/api/workflow-runs?workflowId=${workflow.id}`],
  });

  const latestRunId = runId || (workflowRuns && workflowRuns.length > 0 ? workflowRuns[0].id : undefined);

  // Get the selected run details
  const { data: selectedRun, isLoading: isRunLoading } = useQuery<WorkflowRun>({
    queryKey: [`/api/workflow-runs/${latestRunId}`],
    enabled: !!latestRunId,
  });

  // Get the node runs for the selected workflow run
  const { data: nodeRuns, isLoading: isNodeRunsLoading } = useQuery<NodeRun[]>({
    queryKey: [`/api/workflow-runs/${latestRunId}/node-runs`],
    enabled: !!latestRunId,
  });

  // Get log entries for the selected run
  const { data: logEntries, isLoading: isLogEntriesLoading } = useQuery<LogEntry[]>({
    queryKey: [`/api/workflow-runs/${latestRunId}/logs`],
    enabled: !!latestRunId,
  });

  const isLoading = isRunsLoading || isRunLoading || isNodeRunsLoading || isLogEntriesLoading;

  const getNodeIcon = (nodeType: string) => {
    switch (nodeType) {
      case 'input':
        return <ArrowRight className="text-[#0078D4]" size={16} />;
      case 'prompt':
        return <MessageSquare className="text-[#FF8C00]" size={16} />;
      case 'agent':
        return <Bot className="text-[#107C10]" size={16} />;
      case 'api':
        return <Code className="text-[#5C2D91]" size={16} />;
      default:
        return <div className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="text-green-600 dark:text-green-500" size={16} />;
      case 'failed':
        return <XCircle className="text-red-600 dark:text-red-500" size={16} />;
      case 'running':
        return <Clock className="text-blue-600 dark:text-blue-500" size={16} />;
      default:
        return <AlertCircle className="text-gray-400" size={16} />;
    }
  };

  const formatDuration = (startTime: string, endTime: string | null) => {
    if (!endTime) return 'Running...';

    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const durationMs = end - start;

    if (durationMs < 1000) return `${durationMs}ms`;
    return `${(durationMs / 1000).toFixed(1)}s`;
  };

  const getLogLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <XCircle className="text-red-500" size={14} />;
      case 'warn':
        return <AlertTriangle className="text-yellow-500" size={14} />;
      case 'info':
        return <Info className="text-blue-500" size={14} />;
      case 'debug':
        return <Bug className="text-gray-500" size={14} />;
      default:
        return <FileText className="text-gray-400" size={14} />;
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'warn':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'info':
        return 'text-blue-600 dark:text-blue-400';
      case 'debug':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-gray-500';
    }
  };

  const handleExportLogs = () => {
    if (!selectedRun || !nodeRuns) return;

    const runData = {
      workflow: {
        id: workflow.id,
        name: workflow.name
      },
      run: selectedRun,
      nodeRuns: nodeRuns,
      logEntries: logEntries || [],
      exportedAt: new Date().toISOString(),
      summary: {
        totalNodes: selectedRun.totalNodes || nodeRuns.length,
        completedNodes: selectedRun.completedNodes || nodeRuns.filter(nr => nr.status === 'completed').length,
        failedNodes: selectedRun.failedNodes || nodeRuns.filter(nr => nr.status === 'failed').length,
        executionDuration: selectedRun.executionDuration,
        logEntriesCount: logEntries?.length || 0
      }
    };

    const dataStr = JSON.stringify(runData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;

    const exportFileDefaultName = `workflow-run-${selectedRun.id}-${format(new Date(selectedRun.startTime), 'yyyy-MM-dd')}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  if (isLoading || !selectedRun) {
    return (
      <Dialog open={true} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Workflow Execution: {workflow.name}</DialogTitle>
            <DialogDescription>Loading execution details and status information...</DialogDescription>
          </DialogHeader>
          <div className="py-8 text-center">
            <Clock className="mx-auto h-12 w-12 text-gray-400 animate-pulse" />
            <p className="mt-4 text-gray-500">Loading run details...</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto overflow-x-hidden">
        <DialogHeader>
          <DialogTitle>Workflow Execution: {workflow.name}</DialogTitle>
          <DialogDescription>
            View detailed execution status, logs, and results for this workflow run.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Node Details</TabsTrigger>
            <TabsTrigger value="logs">Logs</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <span className={`px-2 py-1 text-sm rounded-full flex items-center space-x-1 ${
                  selectedRun.status === 'completed' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' :
                  selectedRun.status === 'failed' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' :
                  'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400'
                }`}>
                  {getStatusIcon(selectedRun.status)}
                  <span className="ml-1 capitalize">{selectedRun.status}</span>
                </span>
                <span className="ml-3 text-sm text-neutral-500 dark:text-neutral-400">Run ID: {selectedRun.id}</span>
              </div>
              <div className="text-sm text-neutral-500 dark:text-neutral-400">
                Duration: {formatDuration(selectedRun.startTime, selectedRun.endTime)}
              </div>
            </div>

            <div className="border rounded-md p-4 bg-neutral-50 dark:bg-neutral-900">
              <h3 className="font-medium mb-2">Run Details</h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-neutral-500 dark:text-neutral-400">Start Time:</span>
                  <span className="ml-2">{format(new Date(selectedRun.startTime), 'yyyy-MM-dd HH:mm:ss')}</span>
                </div>
                <div>
                  <span className="text-neutral-500 dark:text-neutral-400">End Time:</span>
                  <span className="ml-2">
                    {selectedRun.endTime
                      ? format(new Date(selectedRun.endTime), 'yyyy-MM-dd HH:mm:ss')
                      : 'Running...'}
                  </span>
                </div>
                <div>
                  <span className="text-neutral-500 dark:text-neutral-400">Trigger Type:</span>
                  <span className="ml-2 capitalize">{selectedRun.triggerType}</span>
                </div>
                <div>
                  <span className="text-neutral-500 dark:text-neutral-400">Total Nodes:</span>
                  <span className="ml-2">{selectedRun.totalNodes || nodeRuns?.length || 0}</span>
                </div>
                {selectedRun.completedNodes !== undefined && (
                  <div>
                    <span className="text-neutral-500 dark:text-neutral-400">Completed:</span>
                    <span className="ml-2 text-green-600 dark:text-green-400">{selectedRun.completedNodes}</span>
                  </div>
                )}
                {selectedRun.failedNodes !== undefined && selectedRun.failedNodes > 0 && (
                  <div>
                    <span className="text-neutral-500 dark:text-neutral-400">Failed:</span>
                    <span className="ml-2 text-red-600 dark:text-red-400">{selectedRun.failedNodes}</span>
                  </div>
                )}
                {selectedRun.executionDuration && (
                  <div>
                    <span className="text-neutral-500 dark:text-neutral-400">Duration:</span>
                    <span className="ml-2">{selectedRun.executionDuration}ms</span>
                  </div>
                )}
              </div>
            </div>

            {selectedRun.errorMessage && (
              <div className="border border-red-200 dark:border-red-800 rounded-md p-4 bg-red-50 dark:bg-red-900/20">
                <h3 className="font-medium mb-2 text-red-800 dark:text-red-400">Workflow Error</h3>
                <p className="text-sm text-red-700 dark:text-red-300 mb-2">{selectedRun.errorMessage}</p>
                {selectedRun.stackTrace && (
                  <details className="mt-2">
                    <summary className="text-sm text-red-600 dark:text-red-400 cursor-pointer hover:text-red-800 dark:hover:text-red-300">
                      View stack trace
                    </summary>
                    <pre className="mt-2 text-xs bg-red-100 dark:bg-red-900/40 p-2 rounded overflow-x-auto text-red-800 dark:text-red-300">
                      {selectedRun.stackTrace}
                    </pre>
                  </details>
                )}
              </div>
            )}

            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-2">Input Data</h3>
              <div className="max-h-48 overflow-y-auto">
                <pre className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded text-sm font-mono whitespace-pre-wrap break-all overflow-x-auto">
                  {JSON.stringify(selectedRun.input, null, 2) || '{}'}
                </pre>
              </div>
            </div>

            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-2">Node Execution Summary</h3>
              <div className="space-y-2">
                {nodeRuns?.map(nodeRun => {
                  const node = workflow.nodes[nodeRun.nodeId];
                  return (
                    <div key={nodeRun.id} className="flex items-center justify-between p-2 bg-neutral-50 dark:bg-neutral-900 rounded">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full flex items-center justify-center bg-white dark:bg-neutral-800 mr-2">
                          {node && getNodeIcon(node.type)}
                        </div>
                        <span className="font-medium text-sm">{node?.data.name || nodeRun.nodeId}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <span className="flex items-center text-sm">
                          {getStatusIcon(nodeRun.status)}
                          <span className="ml-1 capitalize">{nodeRun.status}</span>
                        </span>
                        <span className="text-xs text-neutral-500 dark:text-neutral-400">
                          {formatDuration(nodeRun.startTime, nodeRun.endTime)}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="details">
            <Accordion type="single" collapsible className="w-full">
              {nodeRuns?.map(nodeRun => {
                const node = workflow.nodes[nodeRun.nodeId];
                return (
                  <AccordionItem key={nodeRun.id} value={nodeRun.id.toString()}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full flex items-center justify-center bg-white dark:bg-neutral-800 mr-2">
                          {node && getNodeIcon(node.type)}
                        </div>
                        <span className="font-medium">{node?.data.name || nodeRun.nodeId}</span>
                        <span className="ml-3 flex items-center text-sm">
                          {getStatusIcon(nodeRun.status)}
                          <span className="ml-1 text-xs capitalize">{nodeRun.status}</span>
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3 p-2">
                        <div>
                          <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">Input:</h4>
                          <div className="max-h-48 overflow-y-auto">
                            <pre className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded text-sm font-mono whitespace-pre-wrap break-all overflow-x-auto">
                              {JSON.stringify(nodeRun.input, null, 2) || '{}'}
                            </pre>
                          </div>
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Output:</h4>
                          </div>
                          <div className="max-h-48 overflow-y-auto">
                            <pre className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded text-sm font-mono whitespace-pre-wrap break-all overflow-x-auto">
                              {typeof nodeRun.output === 'object'
                                ? JSON.stringify(nodeRun.output, null, 2)
                                : nodeRun.output || 'No output'}
                            </pre>
                          </div>
                        </div>

                        {nodeRun.error && (
                          <div>
                            <h4 className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">Error:</h4>
                            <pre className="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-300 p-3 rounded text-sm font-mono overflow-x-auto">
                              {nodeRun.error}
                            </pre>
                            {nodeRun.stackTrace && (
                              <details className="mt-2">
                                <summary className="text-sm text-red-600 dark:text-red-400 cursor-pointer hover:text-red-800 dark:hover:text-red-300">
                                  View stack trace
                                </summary>
                                <pre className="mt-2 text-xs bg-red-100 dark:bg-red-900/40 p-2 rounded overflow-x-auto text-red-800 dark:text-red-300">
                                  {nodeRun.stackTrace}
                                </pre>
                              </details>
                            )}
                          </div>
                        )}

                        <div className="grid grid-cols-2 gap-4 text-xs text-neutral-500 dark:text-neutral-400 mt-3 pt-3 border-t">
                          <div>
                            <span>Execution time:</span>
                            <span className="ml-1 font-mono">
                              {nodeRun.executionDuration ? `${nodeRun.executionDuration}ms` : formatDuration(nodeRun.startTime, nodeRun.endTime)}
                            </span>
                          </div>
                          {nodeRun.retryCount !== undefined && nodeRun.retryCount > 0 && (
                            <div>
                              <span>Retries:</span>
                              <span className="ml-1 font-mono">{nodeRun.retryCount}</span>
                            </div>
                          )}
                          {nodeRun.memoryUsage && (
                            <div>
                              <span>Memory:</span>
                              <span className="ml-1 font-mono">{(nodeRun.memoryUsage / 1024 / 1024).toFixed(2)} MB</span>
                            </div>
                          )}
                          {nodeRun.cpuTime && (
                            <div>
                              <span>CPU time:</span>
                              <span className="ml-1 font-mono">{nodeRun.cpuTime}ms</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </TabsContent>

          <TabsContent value="logs" className="space-y-4">
            <div className="border rounded-md p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Execution Logs</h3>
                <div className="text-sm text-neutral-500 dark:text-neutral-400">
                  {logEntries?.length || 0} entries
                </div>
              </div>

              {logEntries && logEntries.length > 0 ? (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {logEntries.map((logEntry) => (
                    <div key={logEntry.id} className="border-l-2 border-gray-200 dark:border-gray-700 pl-3 py-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-2">
                          {getLogLevelIcon(logEntry.level)}
                          <span className={`text-sm font-medium ${getLogLevelColor(logEntry.level)}`}>
                            {logEntry.level.toUpperCase()}
                          </span>
                          <span className="text-xs text-neutral-500 dark:text-neutral-400">
                            {logEntry.source}
                          </span>
                          {logEntry.category && (
                            <span className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                              {logEntry.category}
                            </span>
                          )}
                        </div>
                        <span className="text-xs text-neutral-500 dark:text-neutral-400">
                          {format(new Date(logEntry.timestamp), 'HH:mm:ss.SSS')}
                        </span>
                      </div>

                      <div className="mt-1">
                        <p className="text-sm text-neutral-700 dark:text-neutral-300">
                          {logEntry.message}
                        </p>

                        {logEntry.details && Object.keys(logEntry.details).length > 0 && (
                          <details className="mt-2">
                            <summary className="text-xs text-neutral-500 dark:text-neutral-400 cursor-pointer hover:text-neutral-700 dark:hover:text-neutral-300">
                              View details
                            </summary>
                            <pre className="mt-1 text-xs bg-neutral-50 dark:bg-neutral-900 p-2 rounded overflow-x-auto">
                              {JSON.stringify(logEntry.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-neutral-500 dark:text-neutral-400">
                  <FileText className="mx-auto h-12 w-12 mb-2" />
                  <p>No log entries found for this workflow run</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleExportLogs}>
            <Download className="w-4 h-4 mr-1.5" /> Export Logs
          </Button>
          <Button onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RunStatusModal;
